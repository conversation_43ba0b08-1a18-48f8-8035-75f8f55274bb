<template>
	<el-alert v-if="isUpgrade" type="warning" class="!mb-[1.2rem] !p-[1.2rem]" :closable="false" show-icon>
		<template v-slot:title>
			<span class="text-[1.3rem]">
				<span> 提示：有一个新版本（{{ cloudVersionInfo?.version }}）可更新，更新时间：{{ cloudVersionInfo?.uptime }}</span>
				<el-button class="!ml-[1.2rem]" type="primary" @click="updateService">立即更新</el-button>
				<el-popover placement="bottom-start" width="200" trigger="hover" v-if="cloudVersionInfo?.updateMsg">
					<template #default>
						<div class="update-details" v-html="cloudVersionInfo?.updateMsg"></div>
					</template>
					<template #reference>
						<span class="!ml-[1.2rem] bt-link text-[12px]">详情</span>
					</template>
				</el-popover>
			</span>
		</template>
	</el-alert>
</template>

<script lang="ts" setup>
import { compareVersion } from '@/utils'
import { useSettingsStore } from '@/views/vhost/views/settings/useStore'
import { updateServiceApi } from '@/api/vhost'
import { useDataHandle } from '@/hooks/tools'

const isUpgrade = ref(false)
const { version, cloudVersionInfo, getInfo } = useSettingsStore()

const getIsUpgrade = () => {
	// 使用 cloudVersionInfo 进行版本比较
	if (cloudVersionInfo.value && cloudVersionInfo.value.version && cloudVersionInfo.value.version.trim() !== '') {
		isUpgrade.value = !!compareVersion(version.value, cloudVersionInfo.value.version)
	} else {
		isUpgrade.value = false
	}
}

const updateService = () => {
	useDataHandle({
		loading: '更新中...',
		request: updateServiceApi(),
		message: true,
		success: async () => {
			// 更新完成后重新获取服务信息
			await getInfo()
			getIsUpgrade()
		},
	})
}

onMounted(async () => {
	getIsUpgrade()
})
</script>

<style scoped></style>
